import React, { useEffect, useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@renderer/components/ui/card';
import { Switch } from '@renderer/components/ui/switch';
import { Button } from '@renderer/components/ui/button';
import { Input } from '@renderer/components/ui/input';
import { Label } from '@renderer/components/ui/label';
import { Provider, ProviderType } from '@renderer/types';
import { getProviders, saveProvider, deleteProvider } from '@renderer/service/configService';
import ProviderFactory from '@renderer/providers/AiProvider/providerFactory';
import { SYSTEM_MODELS } from '@renderer/config/model';
import { CheckIcon, XIcon, , LoaderIcon } from 'lucide-react';

// 定义供应商图标和颜色映射
const PROVIDER_ICONS: Record<ProviderType, React.ReactNode> = {
  'openai': <div className="w-6 h-6 rounded-full bg-green-500 flex items-center justify-center text-white font-bold text-xs">AI</div>,
  'anthropic': <div className="w-6 h-6 rounded-full bg-purple-500 flex items-center justify-center text-white font-bold text-xs">AN</div>,
  'gemini': <div className="w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center text-white font-bold text-xs">GM</div>,
  'qwenlm': <div className="w-6 h-6 rounded-full bg-yellow-500 flex items-center justify-center text-white font-bold text-xs">QW</div>,
  'azure-openai': <div className="w-6 h-6 rounded-full bg-blue-400 flex items-center justify-center text-white font-bold text-xs">AZ</div>,
  'deepseek': <div className="w-6 h-6 rounded-full bg-indigo-500 flex items-center justify-center text-white font-bold text-xs">DS</div>,
};

// 定义供应商显示名称映射
const PROVIDER_NAMES: Record<ProviderType, string> = {
  'openai': 'OpenAI',
  'anthropic': 'Anthropic',
  'gemini': 'Google Gemini',
  'qwenlm': 'Qwen',
  'azure-openai': 'Azure OpenAI',
  'deepseek': 'DeepSeek',
};

// 定义供应商默认API主机
const DEFAULT_API_HOSTS: Record<ProviderType, string> = {
  'openai': 'https://api.openai.com/v1',
  'anthropic': 'https://api.anthropic.com',
  'gemini': 'https://generativelanguage.googleapis.com',
  'qwenlm': 'https://dashscope.aliyuncs.com/api/v1',
  'azure-openai': 'https://YOUR_RESOURCE_NAME.openai.azure.com',
  'deepseek': 'https://api.deepseek.com',
};

// 测试连接状态类型
type ConnectionStatus = 'idle' | 'testing' | 'success' | 'error';

// 供应商配置项组件
const ProviderConfigItem: React.FC<{
  provider: Provider;
  onUpdate: (updatedProvider: Provider) => Promise<void>;
  onDelete: (providerId: string) => Promise<void>;
}> = ({ provider, onUpdate, onDelete }) => {
  const [isEnabled, setIsEnabled] = useState(provider.enabled || false);
  const [apiKey, setApiKey] = useState(provider.apiKey || '');
  const [apiHost, setApiHost] = useState(provider.apiHost || DEFAULT_API_HOSTS[provider.type as ProviderType] || '');
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('idle');
  const [errorMessage, setErrorMessage] = useState('');

  // 处理启用/禁用状态变更
  const handleToggleEnabled = async () => {
    const newState = !isEnabled;
    setIsEnabled(newState);
    await onUpdate({
      ...provider,
      enabled: newState,
      apiKey,
      apiHost
    });
  };

  // 处理API密钥变更
  const handleApiKeyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setApiKey(e.target.value);
    setConnectionStatus('idle');
  };

  // 处理API主机变更
  const handleApiHostChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setApiHost(e.target.value);
    setConnectionStatus('idle');
  };

  // 保存配置
  const handleSave = async () => {
    await onUpdate({
      ...provider,
      enabled: isEnabled,
      apiKey,
      apiHost
    });
  };

  // 测试连接
  const handleTestConnection = async () => {
    try {
      setConnectionStatus('testing');
      setErrorMessage('');

      // 创建临时Provider对象用于测试
      const testProvider: Provider = {
        ...provider,
        apiKey,
        apiHost,
        enabled: true
      };

      // 使用ProviderFactory创建Provider实例
      const providerInstance = ProviderFactory.create(testProvider);

      // 尝试调用简单API进行验证
      await providerInstance.generateText({
        prompt: "System prompt for testing",
        content: "Hello, this is a connection test."
      });

      setConnectionStatus('success');
    } catch (error) {
      console.error('连接测试失败:', error);
      setConnectionStatus('error');
      setErrorMessage(error instanceof Error ? error.message : '未知错误');
    }
  };

  return (
    <Card className="mb-4">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            {PROVIDER_ICONS[provider.type as ProviderType]}
            <CardTitle>{PROVIDER_NAMES[provider.type as ProviderType]}</CardTitle>
          </div>
          <Switch checked={isEnabled} onCheckedChange={handleToggleEnabled} />
        </div>
        <CardDescription>
          {provider.type === 'openai' && '支持GPT-4o、GPT-4等模型'}
          {provider.type === 'anthropic' && '支持Claude 3系列模型'}
          {provider.type === 'gemini' && '支持Gemini 1.5和2.0系列模型'}
          {provider.type === 'deepseek' && '支持DeepSeek系列模型'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor={`${provider.id}-apiKey`}>API密钥</Label>
            <Input
              id={`${provider.id}-apiKey`}
              type="password"
              value={apiKey}
              onChange={handleApiKeyChange}
              placeholder="输入API密钥"
              className="font-mono text-sm"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor={`${provider.id}-apiHost`}>API主机地址 (可选)</Label>
            <Input
              id={`${provider.id}-apiHost`}
              type="text"
              value={apiHost}
              onChange={handleApiHostChange}
              placeholder={DEFAULT_API_HOSTS[provider.type as ProviderType]}
              className="font-mono text-sm"
            />
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <div className="flex items-center">
          {connectionStatus === 'idle' && null}
          {connectionStatus === 'testing' && (
            <div className="flex items-center text-muted-foreground">
              <LoaderIcon className="w-4 h-4 mr-2 animate-spin" />
              <span>测试中...</span>
            </div>
          )}
          {connectionStatus === 'success' && (
            <div className="flex items-center text-green-500">
              <CheckIcon className="w-4 h-4 mr-2" />
              <span>连接成功</span>
            </div>
          )}
          {connectionStatus === 'error' && (
            <div className="flex items-center text-red-500">
              <XIcon className="w-4 h-4 mr-2" />
              <span className="text-xs">{errorMessage.substring(0, 50)}</span>
            </div>
          )}
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleTestConnection}
            disabled={!apiKey || connectionStatus === 'testing'}
          >
            测试连接
          </Button>
          <Button
            variant="default"
            size="sm"
            onClick={handleSave}
          >
            保存
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
};

// 主组件
const ModelConfigPanel: React.FC = () => {
  const [providers, setProviders] = useState<Provider[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // 加载所有供应商配置
  const loadProviders = async () => {
    try {
      setIsLoading(true);
      const providersData = await getProviders();

      // 确保所有支持的供应商类型都存在
      const existingTypes = providersData.map(p => p.type);
      const allProviderTypes: ProviderType[] = ['openai', 'anthropic', 'gemini', 'deepseek', 'qwenlm', 'azure-openai'];

      // 为不存在的供应商类型创建默认配置
      const missingProviders = allProviderTypes
        .filter(type => !existingTypes.includes(type))
        .map(type => ({
          id: `new-${type}`,
          type,
          name: PROVIDER_NAMES[type],
          apiKey: '',
          apiHost: DEFAULT_API_HOSTS[type],
          models: SYSTEM_MODELS[type] || [],
          enabled: false
        }));

      setProviders([...providersData, ...missingProviders]);
    } catch (error) {
      console.error('加载供应商配置失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 更新供应商配置
  const updateProvider = async (updatedProvider: Provider) => {
    try {
      await saveProvider(updatedProvider);
      // 重新加载以获取最新数据
      await loadProviders();
    } catch (error) {
      console.error('更新供应商配置失败:', error);
    }
  };

  // 删除供应商配置
  const deleteProviderConfig = async (providerId: string) => {
    try {
      await deleteProvider(providerId);
      await loadProviders();
    } catch (error) {
      console.error('删除供应商配置失败:', error);
    }
  };

  // 初始加载
  useEffect(() => {
    loadProviders();
  }, []);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-full">
        <LoaderIcon className="w-6 h-6 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 max-w-3xl">
      <h1 className="text-2xl font-bold mb-6">AI模型配置</h1>
      <p className="text-muted-foreground mb-6">
        配置各种AI模型供应商的API密钥和设置，启用或禁用不同的供应商。
      </p>

      <div className="space-y-6">
        {providers.map(provider => (
          <ProviderConfigItem
            key={provider.id}
            provider={provider}
            onUpdate={updateProvider}
            onDelete={deleteProviderConfig}
          />
        ))}
      </div>
    </div>
  );
};

export default ModelConfigPanel;
