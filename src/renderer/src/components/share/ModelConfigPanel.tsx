import { useProviderStore } from '@renderer/store/providerStore';
import { LoaderIcon, SettingsIcon } from 'lucide-react';
import React, { useEffect } from 'react';
import { ProviderConfigItem } from './ProviderConfigItem';

// 主组件
const ModelConfigPanel: React.FC = () => {
  // Store状态和操作
  const { providers, isLoading, loadProviders } = useProviderStore();

  // 初始加载
  useEffect(() => {
    loadProviders();
  }, [loadProviders]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-full">
        <LoaderIcon className="w-6 h-6 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 max-w-3xl">
      <div className="flex items-center gap-3 mb-6">
        <SettingsIcon className="w-6 h-6 text-primary" />
        <h1 className="text-2xl font-bold">AI模型配置</h1>
      </div>

      <p className="text-muted-foreground mb-8">
        配置各种AI模型供应商的API密钥和设置，启用或禁用不同的供应商。测试连接以确保配置正确。
      </p>

      <div className="space-y-4">
        {providers.map(provider => (
          <ProviderConfigItem
            key={provider.id}
            provider={provider}
          />
        ))}
      </div>
    </div>
  );
};

export default ModelConfigPanel;
