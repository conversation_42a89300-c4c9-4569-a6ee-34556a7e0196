import { google } from '@ai-sdk/google';
import { Provider } from '@renderer/types';
import { generateText, streamText } from 'ai';
import { BaseProvider } from './base';

export class GeminiProvider extends BaseProvider {
  constructor(provider: Provider) {
    super(provider);
  }

  async generateText({ prompt, content }: { prompt: string; content: string }): Promise<string> {
    const { text } = await generateText({
      model: google("gemini-1.5-pro-latest"),
      messages: [
        { role: "system", content: prompt },
        { role: "user", content: content }
      ]
    });
    return text;
  }

  streamText({ prompt, content }: { prompt: string; content: string }): ReadableStream<string> {
    const { textStream } = streamText({
      model: google("gemini-1.5-pro-latest"),
      messages: [
        { role: "system", content: prompt },
        { role: "user", content: content }
      ]
    });
    return textStream;
  }
}

export default GeminiProvider;
