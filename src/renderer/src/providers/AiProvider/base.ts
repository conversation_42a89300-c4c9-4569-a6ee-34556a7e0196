import type { Provider} from '@renderer/types';
import { formatApiHost } from '@renderer/lib/urlUtil';

export abstract class BaseProvider {
  protected provider: Provider; // Vercel AI SDK OpenAI client instance
  protected host: string;
  protected apiKey: string;

  constructor(provider: Provider) {
    this.provider = provider;
    this.host = this.getBaseURL();
    this.apiKey = this.getApiKey()
  }
  abstract generateText({ prompt, content }: { prompt: string; content: string }): Promise<string>;
  abstract streamText({ prompt, content }: { prompt: string; content: string }): ReadableStream<string>;

  public getBaseURL(): string {
    const host = this.provider.apiHost
    return formatApiHost(host)
  }

  public getApiKey() {
    // TODO 从配置中获取apiKey
    const keys = this.provider.apiKey;
    return keys;
  }
}