import { openai } from "@ai-sdk/openai";
import { Provider } from '@renderer/types';
import { generateText, streamText } from 'ai';
import { BaseProvider } from './base';

export class OpenAIProvider extends BaseProvider {
  constructor(provider: Provider) {
    super(provider);
  }

  async generateText({ prompt, content }: { prompt: string; content: string }): Promise<string> {
    const { text } = await generateText({
      model: openai("o3-mini"),
      messages: [
        { role: "system", content: prompt },
        { role: "user", content: content }
      ]
    });
    return text;
  }

  streamText({ prompt, content }: { prompt: string; content: string }): ReadableStream<string> {
    const { textStream } = streamText({
      model: openai("o3-mini"),
      messages: [
        { role: "system", content: prompt },
        { role: "user", content: content }
      ]
    });
    return textStream;
  }
}

export default OpenAIProvider;