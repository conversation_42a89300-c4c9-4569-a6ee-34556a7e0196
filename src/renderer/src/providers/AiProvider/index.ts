import { Provider } from '@renderer/types';
import OpenAI from 'openai';
import { BaseProvider } from './base';
import ProviderFactory from './providerFactory';

export interface OpenAIConfig {
  apiKey: string;
  model: string;
  temperature: number;
  maxTokens?: number;
}

// 定义符合OpenAI API要求的消息类型
type MessageRole = 'system' | 'user' | 'assistant';
export interface ChatMessage {
  role: MessageRole;
  content: string;
}

export class OpenAIProvider {
  private client: OpenAI;
  private model: string;
  private temperature: number;
  private maxTokens?: number;

  constructor(config: OpenAIConfig) {
    this.client = new OpenAI({
      apiKey: config.apiKey,
      dangerouslyAllowBrowser: true // 在Electron中允许在渲染进程中使用API
    });
    this.model = config.model || 'gpt-3.5-turbo';
    this.temperature = config.temperature || 0.7;
    this.maxTokens = config.maxTokens;
  }

  async sendMessage(messages: ChatMessage[]) {
    try {
      const response = await this.client.chat.completions.create({
        model: this.model,
        messages: messages,
        temperature: this.temperature,
        max_tokens: this.maxTokens,
        stream: false
      });

      return response.choices[0].message.content;
    } catch (error) {
      console.error('OpenAI API调用失败:', error);
      throw error;
    }
  }

  async sendMessageStream(messages: ChatMessage[], onChunk: (chunk: string) => void) {
    try {
      const stream = await this.client.chat.completions.create({
        model: this.model,
        messages: messages,
        temperature: this.temperature,
        max_tokens: this.maxTokens,
        stream: true
      });

      let fullContent = '';
      for await (const chunk of stream) {
        const content = chunk.choices[0]?.delta?.content || '';
        fullContent += content;
        onChunk(content);
      }

      return fullContent;
    } catch (error) {
      console.error('OpenAI流式API调用失败:', error);
      throw error;
    }
  }
}

export default class AiProvider {
  private sdk: BaseProvider

  constructor(provider: Provider) {
    this.sdk = ProviderFactory.create(provider)
  }

  public async generateText({ prompt, content }: { prompt: string; content: string }): Promise<string> {
    return await this.sdk.generateText({ prompt, content })
  }

  public streamText({ prompt, content }: { prompt: string; content: string }): ReadableStream<string> {
    return this.sdk.streamText({ prompt, content })
  }
}