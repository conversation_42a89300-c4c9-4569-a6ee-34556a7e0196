import { getAIConfig } from '@renderer/config/aiConfig';
import { formatTime } from '@renderer/lib/dateUtil';
import {
  getAllConversations as dbGetAllConversations,
  createConversation as dbCreateConversation,
  clearAllHistory as dbClearAllHistory,
  getMessagesByConversationId as dbGetMessagesByConversationId,
  addMessage as dbAddMessage,
  streamMessageToAI as apiStreamMessageToAI,
} from '@renderer/service/conversationService';
import { create } from 'zustand';


export interface Conversation {
  id: number;
  title: string;
  time: string;
  selected: boolean;
}

export interface Message {
  id: number;
  text: string;
  isUser: boolean;
}

interface AppState {
  /* ---- 全局界面状态 ---- */
  colorTheme: string

  /* ---- 侧边栏界面状态 ---- */
  conversations: Conversation[];
  isLoadingConversations: boolean;
  selectedConversationId: number | null;

  /* ---- 聊天界面状态 ---- */
  messages: Message[];
  isLoadingMessages: boolean;
  isStreamingAIMessage: boolean;
  inputText: string;
  currentStreamingMessageId: number | null;


  /* ---- 模型配置面板状态 ---- */
  isModelConfigPanelOpen: boolean

  /* ===== ACTIONS ===== */
  loadConversations: () => Promise<void>;
  selectConversation: (id: number | null) => Promise<void>; // 允许 null 来关闭会话
  createNewConversationUI: () => void; // 仅更新UI以准备新会话
  clearHistory: () => Promise<void>;

  openModelConfigPanel: () => void;
  closeModelConfigPanel: () => void;
  toggleModelConfigPanel: () => void;

  setInputText: (text: string) => void;
  sendMessage: (currentModelId: number) => Promise<void>; // 发送消息的核心操作

}

export const useStore = create<AppState>((set, get) => ({
  colorTheme: "11",
  conversations: [],
  isLoadingConversations: false,
  selectedConversationId: null,
  messages: [],
  isLoadingMessages: false,
  isStreamingAIMessage: false,
  isModelConfigPanelOpen: false,
  inputText: '',
  currentStreamingMessageId: null,

  loadConversations: async () => {
    set({ isLoadingConversations: true });
    try {
      const dbConversations = await dbGetAllConversations();
      const formattedConversations: Conversation[] = dbConversations.map(conv => ({
        id: conv.id,
        title: conv.title,
        time: formatTime(conv.createdAt),
        selected: get().selectedConversationId === conv.id,
      }));
      set({ conversations: formattedConversations, isLoadingConversations: false });
    } catch (error) {
      console.error('从store加载会话失败:', error);
      set({ isLoadingConversations: false });
    }
  },

  selectConversation: async (id: number | null) => {
    if (get().isStreamingAIMessage) return; // 如果AI正在回复，则不允许切换

    if (id === null) {
      set(state => ({
        selectedConversationId: null,
        messages: [], // 清空消息
        conversations: state.conversations.map(conv => ({ ...conv, selected: false })),
        isModelConfigPanelOpen: false,
        inputText: '', // 清空输入
      }));
      return;
    }

    set(state => ({
      selectedConversationId: id,
      conversations: state.conversations.map(conv => ({
        ...conv,
        selected: conv.id === id,
      })),
      isModelConfigPanelOpen: false, // 选择会话时关闭模型配置
      isLoadingMessages: true,
      messages: [], // 清空旧消息
      inputText: '',
    }));
    try {
      const dbMessages = await dbGetMessagesByConversationId(id);
      const formattedMessages: Message[] = dbMessages.map(msg => ({
        id: msg.id,
        text: msg.content,
        isUser: msg.role === 'user',
      }));
      set({ messages: formattedMessages, isLoadingMessages: false });
    } catch (error) {
      console.error(`从store加载会话 ${id} 的消息失败:`, error);
      set({ isLoadingMessages: false, messages: [] }); // 出错时清空消息
    }
  },

  createNewConversationUI: () => {
    if (get().isStreamingAIMessage) return;
    set(state => ({
      selectedConversationId: null,
      messages: [],
      conversations: state.conversations.map(conv => ({ ...conv, selected: false })),
      isModelConfigPanelOpen: false,
      inputText: ''
    }));
  },

  clearHistory: async () => {
    if (get().isStreamingAIMessage) return;
    set({ isLoadingConversations: true }); // 可以添加一个清理中的状态
    try {
      await dbClearAllHistory();
      set({
        conversations: [],
        selectedConversationId: null,
        messages: [],
        isLoadingConversations: false,
        inputText: '',
      });
    } catch (error) {
      console.error('从store清空历史记录失败:', error);
      set({ isLoadingConversations: false });
    }
  },

  openModelConfigPanel: () => set(state => ({
    isModelConfigPanelOpen: true,
    selectedConversationId: null, // 打开模型配置时，取消选中当前会话
    messages: [],                 // 并清空消息
    conversations: state.conversations.map(c => ({ ...c, selected: false })),
    inputText: '',
  })),
  closeModelConfigPanel: () => set({ isModelConfigPanelOpen: false }),
  toggleModelConfigPanel: () => set(state => {
    const opening = !state.isModelConfigPanelOpen;
    if (opening) {
      return {
        isModelConfigPanelOpen: true,
        selectedConversationId: null,
        messages: [],
        conversations: state.conversations.map(c => ({ ...c, selected: false })),
        inputText: '',
      };
    }
    return { isModelConfigPanelOpen: false };
  }),

  setInputText: (text: string) => set({ inputText: text }),

  sendMessage: async (currentModelId: number) => {
    const textToSend = get().inputText.trim();
    if (!textToSend || get().isStreamingAIMessage) return;

    set({ isStreamingAIMessage: true }); // 开始发送，AI即将响应

    let conversationId = get().selectedConversationId;

    // 1. 如果没有选中的会话，则创建新会话
    if (!conversationId) {
      try {
        const newTitle = textToSend.slice(0, 30) + (textToSend.length > 30 ? '...' : ''); //
        conversationId = await dbCreateConversation(newTitle); //
        const newConversation: Conversation = {
          id: conversationId,
          title: newTitle,
          time: formatTime(new Date().toISOString()),
          selected: true,
        };
        // 更新会话列表，并将新会话设为选中
        set(state => ({
          conversations: [newConversation, ...state.conversations.map(c => ({ ...c, selected: false }))].sort((a, b) => b.id - a.id),
          selectedConversationId: conversationId,
          messages: [], // 新会话消息列表为空
        }));
      } catch (error) {
        console.error('在Store中创建新会话失败:', error);
        set({ isStreamingAIMessage: false });
        // TODO: 向用户显示错误提示
        return;
      }
    }

    const finalConversationId = conversationId as number; // 此刻 conversationId 必为 number

    // 2. 添加用户消息到UI和数据库
    const tempUserMessageId = Date.now(); // UI临时ID
    const userMessage: Message = { id: tempUserMessageId, text: textToSend, isUser: true };
    set(state => ({
      messages: [...state.messages, userMessage],
      inputText: '', // 清空输入框
    }));

    let userMessageDbId: number;
    try {
      userMessageDbId = await dbAddMessage(finalConversationId, 'user', textToSend, currentModelId); //
      set(state => ({ // 用数据库ID更新UI消息
        messages: state.messages.map(msg =>
          msg.id === tempUserMessageId ? { ...msg, id: userMessageDbId } : msg
        ),
      }));
    } catch (error) {
      console.error('在Store中存储用户消息失败:', error);
      set(state => ({ // 从UI移除发送失败的消息
        messages: state.messages.filter(msg => msg.id !== tempUserMessageId),
        isStreamingAIMessage: false, // 重置状态
      }));
      // TODO: 向用户显示错误提示
      return;
    }

    // 3. 准备接收AI回复：添加占位消息
    const tempAiMessageId = Date.now() + 1; // AI消息的临时UI ID
    const aiPlaceholderMessage: Message = { id: tempAiMessageId, text: '', isUser: false };
    set(state => ({
      messages: [...state.messages, aiPlaceholderMessage],
      currentStreamingMessageId: tempAiMessageId,
    }));

    // 检查API Key
    const config = getAIConfig(); //
    if (!config.apiKey) { //
      const noApiKeyResponse = "请先在设置中配置 API密钥才能获取真实回复。"; //
      let dbAiMsgId = tempAiMessageId;
      try {
        dbAiMsgId = await dbAddMessage(finalConversationId, 'assistant', noApiKeyResponse, currentModelId); //
      } catch (e) { console.error("DB Error (no API key flow):", e); }
      set(state => ({
        messages: state.messages.map(msg =>
          msg.id === tempAiMessageId ? { ...msg, id: dbAiMsgId, text: noApiKeyResponse } : msg
        ),
        isStreamingAIMessage: false,
        currentStreamingMessageId: null,
      }));
      return;
    }

    // 4. 调用AI服务并流式处理回复
    let fullResponse = '';
    try {
      await apiStreamMessageToAI( //
        finalConversationId,
        textToSend, // 注意：这里通常需要传递包含历史记录的完整消息列表，而非仅当前用户输入
        (chunk) => { //
          fullResponse += chunk;
          set(state => ({
            messages: state.messages.map(msg =>
              msg.id === state.currentStreamingMessageId ? { ...msg, text: fullResponse } : msg
            ),
          }));
        }
      );
      // 流式结束后，存储完整AI回复到数据库
      const finalAiMessageDbId = await dbAddMessage(finalConversationId, 'assistant', fullResponse, currentModelId); //
      set(state => ({
        messages: state.messages.map(msg =>
          msg.id === state.currentStreamingMessageId ? { ...msg, id: finalAiMessageDbId, text: fullResponse } : msg
        ),
        isStreamingAIMessage: false,
        currentStreamingMessageId: null,
      }));
      // 刷新会话列表，以便更新最后活动时间或标题（如果逻辑支持）
      get().loadConversations();

    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'AI响应失败'; //
      console.error('在Store中AI响应或存储失败:', error);
      let dbErrorMsgId = tempAiMessageId;
      try {
        dbErrorMsgId = await dbAddMessage(finalConversationId, 'assistant', errorMsg, currentModelId); //
      } catch (e) { console.error("DB Error (AI error flow):", e); }
      set(state => ({
        messages: state.messages.map(msg =>
          msg.id === state.currentStreamingMessageId ? { ...msg, id: dbErrorMsgId, text: errorMsg } : msg
        ),
        isStreamingAIMessage: false,
        currentStreamingMessageId: null,
      }));
    }
  },

}))
